#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通过mastaji启动投屏模块
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from core.mastaji import MainWindow

def main():
    """主函数"""
    # 高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)  # 防止子窗口关闭导致程序退出
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 延迟启动投屏模块测试
    def test_screen_dlan():
        print("开始测试投屏模块...")
        try:
            window.show_screen_dlan()
            print("投屏模块启动成功")
            
            # 5秒后关闭投屏模块
            def close_screen():
                print("关闭投屏模块...")
                window.close_screen_dlan()
                print("投屏模块关闭完成")
                
                # 再等2秒后退出程序
                QTimer.singleShot(2000, app.quit)
            
            QTimer.singleShot(5000, close_screen)
            
        except Exception as e:
            print(f"测试投屏模块失败: {e}")
            QTimer.singleShot(1000, app.quit)
    
    # 2秒后开始测试
    QTimer.singleShot(2000, test_screen_dlan)
    
    return app.exec_()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
