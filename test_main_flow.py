#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通过main.py启动流程的投屏功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from core.dashboard import Dashboard
from core.mastaji import MainWindow

def main():
    """主函数"""
    # 高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)  # 防止子窗口关闭导致程序退出
    
    # 模拟main.py的启动流程
    print("模拟main.py启动流程...")
    
    # 创建Dashboard
    dashboard = Dashboard()
    dashboard.show()
    
    # 延迟模拟进入课堂
    def enter_classroom():
        print("模拟进入课堂...")
        try:
            # 模拟Dashboard的enter_classroom方法
            mastaji_window = MainWindow(teacher_info={'teacher_name': '测试教师'}, current_course_id='test_course')
            mastaji_window.show()
            dashboard.hide()
            
            # 延迟启动投屏模块测试
            def test_screen_dlan():
                print("开始测试投屏模块...")
                try:
                    mastaji_window.show_screen_dlan()
                    print("投屏模块启动成功")
                    
                    # 5秒后关闭投屏模块
                    def close_screen():
                        print("关闭投屏模块...")
                        mastaji_window.close_screen_dlan()
                        print("投屏模块关闭完成")
                        
                        # 再等2秒后退出程序
                        QTimer.singleShot(2000, app.quit)
                    
                    QTimer.singleShot(5000, close_screen)
                    
                except Exception as e:
                    print(f"测试投屏模块失败: {e}")
                    QTimer.singleShot(1000, app.quit)
            
            # 2秒后开始测试投屏
            QTimer.singleShot(2000, test_screen_dlan)
            
        except Exception as e:
            print(f"进入课堂失败: {e}")
            QTimer.singleShot(1000, app.quit)
    
    # 3秒后模拟进入课堂
    QTimer.singleShot(3000, enter_classroom)
    
    return app.exec_()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
