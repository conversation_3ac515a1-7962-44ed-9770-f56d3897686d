import sys
import threading
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QPushButton, QComboBox, QLabel, QMessageBox
from PyQt5.QtCore import QThread, pyqtSignal,Qt,QEvent
from flask import Flask, send_from_directory
import socket
import upnpy
import requests
from xml.sax.saxutils import escape
import subprocess
import os
import glob
import platform


class ScreenStreamer:
    """屏幕捕获和流媒体服务"""

    def __init__(self, port=8554):
        self.port = port
        self.running = False
        self.ffmpeg_process = None
        self.app = Flask(__name__)

        # 定义流媒体路由
        @self.app.route('/stream.m3u8')
        def stream_hls():
            return send_from_directory('.', 'stream.m3u8')  # 返回 HLS 播放列表文件

        @self.app.route('/<path:filename>')
        def send_file(filename):
            return send_from_directory('.', filename)  # 返回 .ts 文件

    def start_http_server(self):
        """启动HTTP服务器，提供H.264视频流"""
        try:
            self.app.run(host='0.0.0.0', port=self.port, debug=False, use_reloader=False)
        except Exception as e:
            print(f"HTTP服务器启动失败: {e}")
            raise

    def start_capture(self):
        """使用 FFmpeg 捕获屏幕内容并保存为 HLS 格式"""
        self.running = True

        os_name = platform.system()
        if os_name == "Windows":
            input_format = 'gdigrab'
            input_source = 'desktop'
        elif os_name == "Linux":
            input_format = 'x11grab'
            input_source = os.environ.get('DISPLAY', ':0.0')
        else:
            print(f"不支持的操作系统: {os_name}")
            self.stop()
            return

        # 启动 FFmpeg 进程
        command = [
            # 'ffmpeg',
            # '-f', input_format,
            # '-framerate', '30',  
            # '-i', input_source,
            # '-vf', 'scale=1280:720:force_original_aspect_ratio=decrease',
            # '-c:v', 'libx264',  
            # '-preset', 'ultrafast',
            # '-tune', 'zerolatency',
            # '-pix_fmt', 'yuv420p',
            # '-b:v', '1M',
            # '-f', 'hls',
            # '-hls_time', '1',  
            # '-hls_list_size', '3',
            # '-hls_flags', 'delete_segments',
            # 'stream.m3u8'

            'ffmpeg',
            '-f', input_format,
            '-video_size', '1920x1080',
            '-framerate', '30',
            '-i', input_source,
            '-vf', 'scale=1920:1080:force_original_aspect_ratio=decrease',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-pix_fmt', 'yuv420p',
            '-f','rtsp',
            'rtsp://localhost:8554/desktop'

        ]
        try:
            self.ffmpeg_process = subprocess.Popen(
                command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            print("屏幕流媒体编码器已启动")
            # 启动一个线程捕获 FFmpeg 的输出
            threading.Thread(target=self.handle_ffmpeg_output, args=(
                self.ffmpeg_process,), daemon=True).start()
        except Exception as e:
            print(f"启动FFmpeg失败: {str(e)}")
            self.stop()

    def handle_ffmpeg_output(self, process):
        for line in process.stderr:
            print(f"FFmpeg: {line.strip()}")

    def start(self):
        """启动流媒体服务和屏幕捕获"""
        try:
            if self.running:
                print("流媒体服务已在运行")
                return

            # 启动HTTP服务器
            http_thread = threading.Thread(target=self.start_http_server, daemon=True)
            http_thread.start()

            # 启动屏幕捕获
            self.start_capture()

        except Exception as e:
            print(f"启动流媒体服务失败: {e}")
            self.stop()
            raise

    def stop(self):
        """停止服务并删除生成的流文件"""
        self.running = False
        if self.ffmpeg_process:
            self.ffmpeg_process.terminate()
            self.ffmpeg_process.wait()
        self.delete_stream_files()

    def delete_stream_files(self):
        """删除生成的流文件"""
        try:
            for file in glob.glob('stream*'):
                os.remove(file)
            print("已删除生成的流文件")
        except Exception as e:
            print(f"删除流文件失败: {str(e)}")


class DLNADeviceDiscoveryThread(QThread):
    """用于在后台发现DLNA设备的线程"""
    devices_found = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def run(self):
        try:
            upnp = upnpy.UPnP()
            devices = upnp.discover()
            valid_devices = [
                device for device in devices if 'AVTransport' in device.services]
            self.devices_found.emit(valid_devices)
        except Exception as e:
            print(f"设备发现失败: {e}")
            self.error_occurred.emit(str(e))
            self.devices_found.emit([])  # 发送空列表


class ScreenDLAN(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        print(">>> ScreenDLAN 初始化开始")
        try:
            # 设置窗口标志 - 避免使用 WindowStaysOnTopHint 以防止与主窗口冲突
            self.setWindowFlags(Qt.Window)
            # self.setAttribute(Qt.WA_DeleteOnClose, False)  # 关闭时不立即销毁，由主程序控制
            # self.setAttribute(Qt.WA_ShowWithoutActivating, False)  # 确保窗口可以获得焦点
            # self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | self.windowFlags())
            # self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
            # 初始化变量
            self.streamer = None
            self.selected_device = None
            self.devices = []
            self.discovery_thread = None
            self._is_closing = False  # 添加关闭状态标志

            # 安全初始化流媒体服务
            try:
                self.streamer = ScreenStreamer()
                print("流媒体服务初始化成功")
            except Exception as e:
                print(f"流媒体服务初始化失败: {e}")
                self.streamer = None

            self.init_ui()
            self.safe_show()

        except Exception as e:
            print(f"ScreenDLAN 初始化失败: {e}")
            # 显示错误消息但不崩溃
            try:
                QMessageBox.critical(None, "初始化错误", f"投屏模块初始化失败:\n{e}")
            except:
                print("无法显示错误对话框")
            # 设置一个最小的界面以避免完全崩溃
            self.setWindowTitle('投屏模块 - 初始化失败')
            self.setGeometry(800, 600, 300, 200)
            layout = QVBoxLayout()
            error_label = QLabel(f"初始化失败: {e}")
            layout.addWidget(error_label)
            self.setLayout(layout)
   
    def init_ui(self):
        print(">>> ScreenDLAN 初始化UI开始")
        try:
            self.setWindowTitle('DLNA投屏控制')
            self.setGeometry(800, 600, 300, 200)
            

            layout = QVBoxLayout()

            # 状态标签
            self.status_label = QLabel('正在搜索设备...')
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")
            layout.addWidget(self.status_label)

            self.device_label = QLabel('选择设备:')
            layout.addWidget(self.device_label)

            self.device_combo = QComboBox()
            layout.addWidget(self.device_combo)

            # 刷新设备按钮
            self.refresh_button = QPushButton('刷新设备')
            self.refresh_button.clicked.connect(self.refresh_devices)
            layout.addWidget(self.refresh_button)

            self.cast_button = QPushButton('投屏')
            self.cast_button.clicked.connect(self.start_casting)
            self.cast_button.setEnabled(False)  # 初始禁用，等设备发现完成
            layout.addWidget(self.cast_button)

            self.stop_button = QPushButton('停止')
            self.stop_button.clicked.connect(self.stop_casting)
            self.stop_button.setEnabled(False)
            layout.addWidget(self.stop_button)

            self.setLayout(layout)

            # 启动设备发现
            self.start_device_discovery()

        except Exception as e:
            print(f"init_ui 失败: {e}")
            try:
                QMessageBox.critical(self, "界面初始化错误", f"界面初始化失败:\n{e}")
            except:
                print("无法显示错误对话框")

    def safe_show(self):
        """安全显示窗口"""
        print(">>> ScreenDLAN 安全显示窗口开始")
        try:
            # 确保应用程序不会退出 - 使用正确的QApplication实例
            app = QApplication.instance()
            if app:
                app.setQuitOnLastWindowClosed(False)

            # 重置关闭状态
            self._is_closing = False

            # 确保窗口创建完成
            if not self.isVisible():
                self.show()  # 首次显示
            else:
                self.showNormal()  # 如果已最小化则恢复

            # 强制窗口置顶并运行
            self.raise_()
            self.activateWindow()

            # 确保窗口获得焦点（Linux系统特别需要）
            if sys.platform == "linux":
                self.setAttribute(Qt.WA_X11NetWmWindowTypeDialog, True)

            # 添加事件过滤器捕获关闭事件
            # self.installEventFilter(self)

            print("投屏窗口已安全显示")
            return True

        except Exception as e:
            print(f"安全显示窗口失败: {e}")
            print(f"窗口状态: visible={self.isVisible()}, active={self.isActiveWindow()}")
            print(f"窗口几何: {self.geometry()}")
            return False
    def eventFilter(self, obj, event):
        """过滤关闭事件"""
        if event.type() == QEvent.Close:
            print(">>> 捕获关闭事件，阻止默认关闭行为")
            # 阻止窗口关闭
            return True
        return super().eventFilter(obj, event)
    
    def start_device_discovery(self):
        """启动设备发现线程"""
        try:
            if self.discovery_thread and self.discovery_thread.isRunning():
                return

            self.discovery_thread = DLNADeviceDiscoveryThread()
            self.discovery_thread.devices_found.connect(self.populate_device_list)
            self.discovery_thread.error_occurred.connect(self.handle_discovery_error)
            self.discovery_thread.finished.connect(self.on_discovery_finished)
            self.discovery_thread.start()

            # 更新状态
            if hasattr(self, 'status_label'):
                self.status_label.setText('正在搜索设备...')
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")

        except Exception as e:
            print(f"启动设备发现失败: {e}")
            if hasattr(self, 'status_label'):
                self.status_label.setText(f'设备发现启动失败: {e}')
                self.status_label.setStyleSheet("color: red; font-weight: bold;")

    def refresh_devices(self):
        """刷新设备列表"""
        self.device_combo.clear()
        self.device_combo.addItem("正在搜索...")
        self.cast_button.setEnabled(False)
        self.start_device_discovery()

    def on_discovery_finished(self):
        """设备发现完成"""
        if hasattr(self, 'status_label'):
            if hasattr(self, 'devices') and self.devices:
                self.status_label.setText(f'发现 {len(self.devices)} 个设备')
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.status_label.setText('未发现设备')
                self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def populate_device_list(self, devices):
        """填充设备列表"""
        try:
            self.devices = devices
            self.device_combo.clear()  # 清空现有列表

            if devices:
                for device in devices:
                    try:
                        device_name = getattr(device, 'friendly_name', '未知设备')
                        self.device_combo.addItem(device_name)
                    except Exception as e:
                        print(f"添加设备到列表失败: {e}")
                        self.device_combo.addItem("设备信息错误")

                # 启用投屏按钮
                self.cast_button.setEnabled(True)
                print(f"发现 {len(devices)} 个DLNA设备")
            else:
                self.device_combo.addItem("未发现设备")
                self.cast_button.setEnabled(False)
                print("未发现任何DLNA设备")

        except Exception as e:
            print(f"填充设备列表失败: {e}")
            self.device_combo.clear()
            self.device_combo.addItem("列表更新失败")
            self.cast_button.setEnabled(False)

    def handle_discovery_error(self, error_msg):
        """处理设备发现错误"""
        try:
            print(f"设备发现错误: {error_msg}")
            self.device_combo.clear()
            self.device_combo.addItem("设备发现失败")
            self.cast_button.setEnabled(False)

            if hasattr(self, 'status_label'):
                self.status_label.setText(f'设备发现失败: {error_msg}')
                self.status_label.setStyleSheet("color: red; font-weight: bold;")

        except Exception as e:
            print(f"处理设备发现错误时出错: {e}")

    def get_device_info(self, dev):
        """获取设备信息"""
        try:
            base_url = dev.base_url
            control_url = dev.services["AVTransport"].control_url
            if not control_url.startswith('http://') and not control_url.startswith('https://'):
                control_url = f"{base_url}{control_url}"
            return {
                "name": dev.friendly_name,
                "control_url": control_url
            }
        except Exception as e:
            print(f"获取设备信息失败: {str(e)}")
            return None

    def build_soap_envelope(self, media_url):
        """构建SOAP请求"""
        metadata = f"""
        <DIDL-Lite xmlns:dc="http://purl.org/dc/elements/1.1/"
                   xmlns:upnp="urn:schemas-upnp-org:metadata-1-0/upnp/"
                   xmlns="urn:schemas-upnp-org:metadata-1-0/DIDL-Lite/">
            <item id="1" parentID="0" restricted="0">
                <dc:title>Live Screen</dc:title>
                <upnp:class>object.item.videoItem</upnp:class>
                <res protocolInfo="http-get:*:video/mp2t:DLNA.ORG_OP=01;DLNA.ORG_CI=0;DLNA.ORG_FLAGS=01500000000000000000000000000000"
                     resolution="1920x1080">{escape(media_url)}</res>
            </item>
        </DIDL-Lite>
        """

        return f"""
        <?xml version="1.0"?>
        <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"
                    s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
            <s:Body>
                <u:SetAVTransportURI xmlns:u="urn:schemas-upnp-org:service:AVTransport:1">
                    <InstanceID>0</InstanceID>
                    <CurrentURI>{escape(media_url)}</CurrentURI>
                    <CurrentURIMetaData>{escape(metadata)}</CurrentURIMetaData>
                </u:SetAVTransportURI>
            </s:Body>
        </s:Envelope>
        """

    def cast_to_device(self, device_info, media_url):
        """执行投屏操作"""
        try:
            headers = {
                'Content-Type': 'text/xml; charset="utf-8"',
                'SOAPAction': '"urn:schemas-upnp-org:service:AVTransport:1#SetAVTransportURI"'
            }
            body = self.build_soap_envelope(media_url)
            response = requests.post(
                device_info['control_url'],
                data=body,
                headers=headers,
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            print(f"投屏请求异常: {str(e)}")
            return False

    def play_control(self, device_info, command='Play'):
        """播放控制命令"""
        actions = {
            'Play': {'action': 'Play', 'params': '<Speed>1</Speed>'},
            'Stop': {'action': 'Stop', 'params': ''}
        }
        try:
            headers = {
                'Content-Type': 'text/xml; charset="utf-8"',
                'SOAPAction': f'"urn:schemas-upnp-org:service:AVTransport:1#{command}"'
            }
            body = f"""
            <?xml version="1.0"?>
            <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"
                        s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
                <s:Body>
                    <u:{command} xmlns:u="urn:schemas-upnp-org:service:AVTransport:1">
                        <InstanceID>0</InstanceID>
                        {actions[command]['params']}
                    </u:{command}>
            </s:Body>
            </s:Envelope>
            """
            response = requests.post(
                device_info['control_url'],
                data=body,
                headers=headers,
                timeout=3
            )
            return response.status_code == 200
        except Exception as e:
            print(f"控制命令{command}失败: {str(e)}")
            return False

    def get_local_ip(self):
        """获取本机有效IP地址"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip=s.getsockname()[0]
            s.close()
            return ip
        except:
            return socket.gethostbyname(socket.gethostname())

    def start_casting(self):
        """开始投屏"""
        print(">>> start_casting开始")
        try:
            # 检查流媒体服务是否可用
            if not self.streamer:
                QMessageBox.critical(self, "错误", "流媒体服务未初始化，无法投屏")
                return

            selected_index = self.device_combo.currentIndex()
            if selected_index < 0:
                print("请先选择一个设备")
                QMessageBox.warning(self, "警告", "请先选择一个投屏设备")
                return

            # 检查设备列表是否存在且有效
            if not hasattr(self, 'devices') or not self.devices:
                print("设备列表为空，正在重新搜索设备...")
                QMessageBox.information(self, "提示", "设备列表为空，正在重新搜索设备...")
                self.refresh_devices()
                return

            # 检查索引是否有效
            if selected_index >= len(self.devices):
                print(f"设备索引无效: {selected_index}, 设备总数: {len(self.devices)}")
                QMessageBox.warning(self, "错误", "选择的设备无效，请重新选择")
                return

            # 更新状态
            if hasattr(self, 'status_label'):
                self.status_label.setText('正在启动投屏...')
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")

            self.selected_device = self.devices[selected_index]
            device_info = self.get_device_info(self.selected_device)
            if not device_info:
                print("无法获取设备信息")
                QMessageBox.warning(self, "错误", "无法获取设备信息，请检查设备连接")
                if hasattr(self, 'status_label'):
                    self.status_label.setText('获取设备信息失败')
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")
                return

            local_ip = self.get_local_ip()
            media_url = f"http://{local_ip}:{self.streamer.port}/stream.m3u8"

            # 先启动流媒体服务
            try:
                print(f"启动流媒体服务，URL: {media_url}")
                self.streamer.start()
            except Exception as e:
                print(f"启动流媒体服务失败: {e}")
                QMessageBox.critical(self, "错误", f"启动流媒体服务失败: {str(e)}")
                if hasattr(self, 'status_label'):
                    self.status_label.setText('流媒体服务启动失败')
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")
                return

            # 尝试投屏
            if self.cast_to_device(device_info, media_url):
                if self.play_control(device_info):
                    print("投屏成功！")
                    QMessageBox.information(self, "成功", "投屏启动成功！")
                    self.cast_button.setEnabled(False)
                    self.stop_button.setEnabled(True)
                    if hasattr(self, 'status_label'):
                        self.status_label.setText('投屏中...')
                        self.status_label.setStyleSheet("color: green; font-weight: bold;")
                else:
                    print("播放控制失败")
                    QMessageBox.warning(self, "警告", "投屏设置成功但播放控制失败")
                    self.streamer.stop()
            else:
                print("投屏失败")
                QMessageBox.warning(self, "失败", "投屏失败，请检查网络连接和设备状态")
                self.streamer.stop()
                if hasattr(self, 'status_label'):
                    self.status_label.setText('投屏失败')
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")

        except Exception as e:
            print(f"投屏过程中发生错误: {e}")
            QMessageBox.critical(self, "严重错误", f"投屏过程中发生错误: {str(e)}")
            # 确保在出错时停止流媒体服务
            try:
                if self.streamer:
                    self.streamer.stop()
            except:
                pass
            if hasattr(self, 'status_label'):
                self.status_label.setText(f'投屏错误: {e}')
                self.status_label.setStyleSheet("color: red; font-weight: bold;")

    def stop_casting(self):
        """停止投屏"""
        try:
            if hasattr(self, 'status_label'):
                self.status_label.setText('正在停止投屏...')
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")

            # 停止设备播放
            if self.selected_device:
                try:
                    device_info = self.get_device_info(self.selected_device)
                    if device_info:
                        self.play_control(device_info, 'Stop')
                        print("设备播放已停止")
                except Exception as e:
                    print(f"停止设备播放失败: {e}")

            # 停止流媒体服务
            if self.streamer:
                try:
                    self.streamer.stop()
                    print("流媒体服务已停止")
                except Exception as e:
                    print(f"停止流媒体服务失败: {e}")

            # 更新界面状态
            self.cast_button.setEnabled(True)
            self.stop_button.setEnabled(False)

            if hasattr(self, 'status_label'):
                self.status_label.setText('投屏已停止')
                self.status_label.setStyleSheet("color: orange; font-weight: bold;")

        except Exception as e:
            print(f"停止投屏时出错: {e}")
            if hasattr(self, 'status_label'):
                self.status_label.setText(f'停止失败: {e}')
                self.status_label.setStyleSheet("color: red; font-weight: bold;")

    def closeEvent(self, event):
        """窗口关闭事件"""
        print(f">>> closeEvent 触发, is_closing: {self._is_closing}") 
        try:
            if not self._is_closing and self.isVisible():
                print("窗口意外关闭尝试被阻止")
                event.ignore()
                return
            
            if self._is_closing:
                event.accept()
                return

            self._is_closing = True
            print("正在关闭投屏窗口...")

            # 确保应用程序不会因为这个窗口关闭而退出
            app = QApplication.instance()
            if app:
                app.setQuitOnLastWindowClosed(False)

            # 停止正在进行的投屏及相关线程
            try:
                self.stop_casting()
            except Exception as e:
                print(f"停止投屏失败: {e}")

            # 停止设备发现线程
            if hasattr(self, 'discovery_thread') and self.discovery_thread:
                try:
                    if self.discovery_thread.isRunning():
                        self.discovery_thread.quit()
                        self.discovery_thread.wait(3000)  # 等待最多3秒
                        if self.discovery_thread.isRunning():
                            self.discovery_thread.terminate()
                except Exception as e:
                    print(f"停止设备发现线程失败: {e}")

            print("投屏窗口关闭完成")

        except Exception as e:
            print(f"关闭投屏窗口时出错: {e}")

        # 隐藏窗口而不是真正关闭，避免触发应用程序退出
        self.hide()
        event.ignore()  # 忽略关闭事件，保持窗口对象存在

        def showEvent(self, event):
            print(">>> showEvent 触发")
            super().showEvent(event)
        
        def hideEvent(self, event):
            print(">>> hideEvent 触发")
            super().hideEvent(event)
        
    def event(self, event):
        if event.type() == QEvent.Close:
            print(">>> QEvent.Close 捕获")
        return super().event(event)
    
# if __name__ == "__main__":
#     app = QApplication(sys.argv)
#     screen_dlan = ScreenDLAN()
#     screen_dlan.show()
#     sys.exit(app.exec_())