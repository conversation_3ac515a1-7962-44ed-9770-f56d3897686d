import json
import os
import sys
import requests
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QWidget, QHBoxLayout, QVBoxLayout,
                             QLabel, QPushButton, QGridLayout, QSpacerItem, QSizePolicy, QGraphicsDropShadowEffect,
                             QMessageBox, QScrollArea)
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor
from PyQt5.QtCore import Qt, QSize, QRect, QTimer
from core.mastaji import MainWindow
from core.login_manager import get_login_manager

def create_avatar_pixmap(size):
        """创建橙色圆形背景和白色字母'M'的头像"""
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        # 背景
        painter.setBrush(QColor("#f9a825")) # 橙色
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, size, size)
        # 字母
        painter.setPen(QColor("white"))
        font = QFont("Arial", size // 2)
        font.setBold(True)
        painter.setFont(font)
        painter.drawText(QRect(0, 0, size, size), Qt.AlignCenter, "M")
        painter.end()
        return pixmap

class Dashboard(QWidget):
    def __init__(self, teacher_info=None):
        super().__init__()
        self.setWindowTitle(" ")
        self.setGeometry(100, 100, 960, 600)
        self.setStyleSheet("background-color: #FFFFFF;")

        # 获取登录管理器
        self.login_manager = get_login_manager()

        # 教师信息 - 优先使用传入的信息，否则从登录管理器获取
        if teacher_info:
            self.teacher_info = teacher_info
            # 同时更新登录管理器
            self.login_manager.teacher_info = teacher_info
            self.login_manager.session = teacher_info.get('session')
            self.login_manager.is_logged_in = True
            self.login_manager.save_session()
        else:
            self.teacher_info = self.login_manager.get_teacher_info() or {}

        self.teacher_name = self.teacher_info.get('teacher_name', '教师')

        # 加载配置
        # 获取项目根目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        # 配置文件完整路径
        self.config_file = os.path.join(project_root, 'config.json')
        self.config = self.load_config()

        # 窗口居中
        self.center_window()

        # 课程数据
        self.courses = []
        # 使用传入的session或从登录管理器获取
        self.session = self.teacher_info.get('session') or self.login_manager.get_session() or requests.Session()

        # 定时器用于更新时间和刷新课程
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time_and_greeting)
        self.timer.start(60000)  # 每分钟更新一次

        # 课程刷新定时器
        self.course_timer = QTimer()
        self.course_timer.timeout.connect(self.refresh_courses)
        self.course_timer.start(300000)  # 每300秒刷新一次课程

        # 主水平布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建并添加左侧边栏
        left_sidebar = self._create_left_sidebar()
        main_layout.addWidget(left_sidebar)

        # 创建并添加右侧主面板
        right_pane = self._create_right_pane()
        main_layout.addWidget(right_pane)

        self.setLayout(main_layout)

        # 初始加载教师信息和课程
        self.load_teacher_info_and_courses()

    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        window_geometry = self.frameGeometry()
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)
        self.move(window_geometry.topLeft())

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")

    def load_teacher_info_and_courses(self):
        """获取教师信息并加载初始课程"""
        try:
            # 如果已经有session（从登录窗口传入），跳过登录步骤
            if self.teacher_info.get('session'):
                print("使用已有的登录session")
                # 如果已经有教师姓名，直接使用；否则尝试获取
                if self.teacher_info.get('teacher_name'):
                    self.teacher_name = self.teacher_info.get('teacher_name')
                    print(f"使用已有教师信息: {self.teacher_name}")
                else:
                    # 尝试从服务器获取教师信息
                    teacher_response = self.session.get(
                        f"{self.config['server']['url']}/teacher/get_teacher_info",
                        timeout=self.config['server']['timeout']
                    )
                    if teacher_response.status_code == 200:
                        teacher_data = teacher_response.json()
                        if teacher_data.get('status') == 'success':
                            teacher_info_data = teacher_data.get('teacher_info', {})
                            self.teacher_name = teacher_info_data.get('name', '教师')
                            print(f"从服务器获取教师信息成功: {self.teacher_name}")
                        else:
                            print("获取教师信息失败，使用默认值")
                    else:
                        print(f"获取教师信息失败，状态码: {teacher_response.status_code}")
            else:
                # 如果没有session，执行登录
                login_data = {
                    'user_id': self.config.get('saved_login', {}).get('user_id', 'T001'),
                    'password': self.config.get('saved_login', {}).get('password', '123')
                }
                login_response = self.session.post(
                    f"{self.config['server']['url']}/login",
                    data=login_data,
                    timeout=self.config['server']['timeout']
                )

                if login_response.status_code == 200:
                    teacher_info_response = self.session.get(
                        f"{self.config['server']['url']}/teacher/get_teacher_info",
                        timeout=self.config['server']['timeout']
                    )
                    if teacher_info_response.status_code == 200:
                        teacher_data = teacher_info_response.json()
                        if teacher_data.get('status') == 'success':
                            self.teacher_info = teacher_data.get('teacher_info', {})
                            self.teacher_name = self.teacher_info.get('name', '教师')
                            self.update_teacher_display()
                        else:
                            print(f"获取教师信息失败: {teacher_data.get('message', '未知错误')}")
                    else:
                        print(f"获取教师信息失败，状态码: {teacher_info_response.status_code}")
                else:
                    print(f"登录失败，状态码: {login_response.status_code}")
                    self.courses = []
                    self.update_course_display()
                    return

            # 初始加载课程
            self.refresh_courses()
        except Exception as e:
            print(f"连接后端失败: {e}")
            self.courses = []
            self.update_course_display()

    def refresh_courses(self):
        """刷新课程安排（供定时器和手动刷新按钮调用）"""
        try:
            # print(f"正在刷新课程... ({datetime.now().strftime('%H:%M:%S')})")
            response = self.session.get(
                f"{self.config['server']['url']}/teacher/get_course_schedules",
                timeout=self.config['server']['timeout']
            )
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    all_courses = data.get('course_schedules', [])
                    scheduled = [c for c in all_courses if c.get('status') == 'scheduled']
                    in_progress = [c for c in all_courses if c.get('status') == 'in_progress']
                    self.courses = in_progress + scheduled
                    self.update_course_display()
                else:
                    print(f"刷新课程失败: {data.get('message', '未知错误')}")
            else:
                # 如果session失效（例如服务器重启），尝试重新登录
                if response.status_code == 401: # Unauthorized
                    print("Session可能已失效，正在尝试重新登录...")
                    self.load_teacher_info_and_courses()
                else:
                    print(f"刷新课程失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"刷新课程时出错: {e}")

    def update_teacher_display(self):
        """更新教师信息显示"""
        if hasattr(self, 'name_label'):
            self.name_label.setText(self.teacher_name)

    def update_time_and_greeting(self):
        """更新时间和问候语"""
        now = datetime.now()
        hour = now.hour

        # 根据时间确定问候语
        if 5 <= hour < 12:
            greeting = "上午好!"
        elif 12 <= hour < 18:
            greeting = "下午好!"
        else:
            greeting = "晚上好!"

        # 更新显示
        if hasattr(self, 'greeting_label'):
            self.greeting_label.setText(greeting)
        if hasattr(self, 'date_label'):
            date_str = now.strftime("%Y年%m月%d日 %A")
            # 转换英文星期为中文
            weekdays = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            for en, zh in weekdays.items():
                date_str = date_str.replace(en, zh)
            self.date_label.setText(date_str)

    def _create_left_sidebar(self):
        """创建左侧边栏"""
        sidebar_widget = QWidget()
        sidebar_widget.setFixedWidth(240)
        sidebar_widget.setStyleSheet("background-color: #f0f2f5;")

        sidebar_layout = QVBoxLayout(sidebar_widget)
        sidebar_layout.setContentsMargins(20, 20, 20, 10)
        sidebar_layout.setSpacing(15)
        sidebar_layout.setAlignment(Qt.AlignTop)

        # 头像
        avatar_label = QLabel()
        raw_pixmap = create_avatar_pixmap(80)
        
        # --- 创建圆形头像 ---
        circular_pixmap = QPixmap(raw_pixmap.size())
        circular_pixmap.fill(Qt.transparent)
        mask_painter = QPainter(circular_pixmap)
        mask_painter.setRenderHint(QPainter.Antialiasing)
        mask_painter.setBrush(Qt.black)
        mask_painter.drawEllipse(0, 0, raw_pixmap.width(), raw_pixmap.height())
        mask_painter.end()
        
        raw_pixmap.setMask(circular_pixmap.mask())
        avatar_label.setPixmap(raw_pixmap)
        avatar_label.setAlignment(Qt.AlignCenter)
        
        # 用户名
        self.name_label = QLabel(self.teacher_name)
        self.name_label.setFont(QFont("微软雅黑", 14))
        self.name_label.setAlignment(Qt.AlignCenter)

        sidebar_layout.addWidget(avatar_label)
        sidebar_layout.addWidget(self.name_label)
        sidebar_layout.addSpacing(20)

        # 功能按钮网格
        grid_layout = QGridLayout()
        grid_layout.setSpacing(15)

        buttons_info = [
            {"text": "上课", "text_icon": "📚", "color": "#4a90e2"},
        ]

        positions = [(i, j) for i in range(2) for j in range(2)]

        for position, info in zip(positions, buttons_info):
            button = QPushButton(f" {info['text']}")
            button.setText(f"{info['text_icon']} {info['text']}")  # 使用文字图标代替
            button.setIconSize(QSize(20, 20))  # 文字图标大小
            button.setFont(QFont("微软雅黑", 11))
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {info['color']};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background-color: {QColor(info['color']).lighter(115).name()};
                }}
            """)
            grid_layout.addWidget(button, *position)
            
        sidebar_layout.addLayout(grid_layout)

        # 弹簧, 将底部内容推到底部
        sidebar_layout.addSpacerItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # 底部工具
        bottom_layout = QHBoxLayout()
        bottom_layout.setContentsMargins(0,0,0,0)

        # 退出登录按钮
        logout_btn = QPushButton("🚪 退出登录")
        logout_btn.setFont(QFont("微软雅黑", 10))
        logout_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                text-align: center;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #c62828;
            }
        """)
        logout_btn.setCursor(Qt.PointingHandCursor)
        logout_btn.clicked.connect(self.logout)

        bottom_layout.addWidget(logout_btn)
        bottom_layout.addStretch()

        sidebar_layout.addLayout(bottom_layout)
        
        return sidebar_widget

    def _create_right_pane(self):
        """创建右侧主面板"""
        pane_widget = QWidget()
        pane_layout = QVBoxLayout(pane_widget)
        pane_layout.setContentsMargins(40, 25, 40, 40)
        pane_layout.setSpacing(20)
        pane_layout.setAlignment(Qt.AlignTop)

        # 顶部栏：问候语和图标
        top_bar_layout = QHBoxLayout()
        top_bar_layout.setSpacing(10)

        # 问候语和日期
        greeting_layout = QVBoxLayout()
        greeting_layout.setSpacing(5)

        # 获取当前时间和问候语
        now = datetime.now()
        hour = now.hour
        if 5 <= hour < 12:
            greeting_text = "上午好!"
        elif 12 <= hour < 18:
            greeting_text = "下午好!"
        else:
            greeting_text = "晚上好!"

        self.greeting_label = QLabel(greeting_text)
        self.greeting_label.setFont(QFont("微软雅黑", 24, QFont.Bold))
        self.greeting_label.setStyleSheet("color: #333;")

        # 格式化日期
        date_str = now.strftime("%Y年%m月%d日 %A")
        weekdays = {
            'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
            'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
        }
        for en, zh in weekdays.items():
            date_str = date_str.replace(en, zh)

        self.date_label = QLabel(date_str)
        self.date_label.setFont(QFont("微软雅黑", 11))
        self.date_label.setStyleSheet("color: #888;")

        greeting_layout.addWidget(self.greeting_label)
        greeting_layout.addWidget(self.date_label)
        
        top_bar_layout.addLayout(greeting_layout)
        top_bar_layout.addStretch()
        
        settings_btn = QPushButton()
        settings_btn.setText('设置')  # 使用齿轮emoji作为设置图标
        settings_btn.setStyleSheet("border:none;")
        settings_btn.setCursor(Qt.PointingHandCursor)
        
        help_btn = QPushButton()
        help_btn.setText('帮助')  # 使用问号emoji作为帮助图标
        help_btn.setStyleSheet("border:none;")
        help_btn.setCursor(Qt.PointingHandCursor)

        top_bar_layout.addWidget(settings_btn, alignment=Qt.AlignTop)
        top_bar_layout.addWidget(help_btn, alignment=Qt.AlignTop)

        pane_layout.addLayout(top_bar_layout)
        pane_layout.addSpacing(20)

        # 课程安排和刷新按钮
        course_schedule_layout = QHBoxLayout()
        course_schedule_layout.setSpacing(10)
        
        course_schedule_label = QLabel("课程安排")
        course_schedule_label.setFont(QFont("微软雅黑", 11, QFont.Bold))
        course_schedule_label.setStyleSheet("color: #333;")
        course_schedule_layout.addWidget(course_schedule_label)
        
        course_schedule_layout.addStretch()

        refresh_btn = QPushButton("刷新")
        refresh_btn.setFont(QFont("Arial", 12))
        refresh_btn.setStyleSheet("border: none; color: #555; padding-bottom: 3px;")
        refresh_btn.setCursor(Qt.PointingHandCursor)
        refresh_btn.setToolTip("刷新课程安排")
        refresh_btn.clicked.connect(self.refresh_courses)
        course_schedule_layout.addWidget(refresh_btn)

        pane_layout.addLayout(course_schedule_layout)

        # 课程显示区域（使用滚动区域）
        self.course_scroll_area = QScrollArea()
        self.course_scroll_area.setWidgetResizable(True)
        self.course_scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 4px;
            }
        """)

        self.course_widget = QWidget()
        self.course_layout = QVBoxLayout(self.course_widget)
        self.course_layout.setSpacing(15)
        self.course_layout.setContentsMargins(0, 0, 0, 0)

        self.course_scroll_area.setWidget(self.course_widget)
        pane_layout.addWidget(self.course_scroll_area)

        # 弹簧
        pane_layout.addStretch()

        return pane_widget

    def update_course_display(self):
        """更新课程显示"""
        # 清除现有的课程卡片
        for i in reversed(range(self.course_layout.count())):
            child = self.course_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.courses:
            # 没有课程安排时显示提示
            no_course_label = QLabel("没有课程")
            no_course_label.setFont(QFont("微软雅黑", 16))
            no_course_label.setStyleSheet("""
                color: #888;
                padding: 40px;
                text-align: center;
                background-color: #f8f9fa;
                border-radius: 12px;
                border: 2px dashed #ddd;
            """)
            no_course_label.setAlignment(Qt.AlignCenter)
            self.course_layout.addWidget(no_course_label)
        else:
            # 显示课程安排（正在进行的课程显示在前面）
            for course in self.courses:
                course_card = self._create_class_card(course)
                self.course_layout.addWidget(course_card)

    def _create_class_card(self, course_data=None):
        """创建课程卡片"""
        card_widget = QWidget()
        card_widget.setMinimumHeight(100)

        # 根据课程状态设置不同的背景色
        status = course_data.get('status', 'scheduled')
        if status == 'in_progress':
            # 正在进行的课程使用绿色背景
            background_color = "#e8f5e8"
        else:
            # 已安排的课程使用默认背景
            background_color = "#f8f9fa"

        card_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {background_color};
                border-radius: 12px;
            }}
        """)

        card_layout = QHBoxLayout(card_widget)
        card_layout.setContentsMargins(20, 20, 20, 20)
        card_layout.setSpacing(15)

        # 左侧状态指示器
        status_widget = QWidget()
        status_widget.setFixedWidth(70)
        status_layout = QVBoxLayout(status_widget)
        status_layout.setAlignment(Qt.AlignTop)
        status_layout.setContentsMargins(0, 0, 0, 0)

        if status == 'in_progress':
            status_label = QLabel("正在进行")
            status_label.setStyleSheet("""
                background-color: #4caf50;
                color: white;
                padding: 4px 8px;
                border-radius: 10px;
                font-size: 8px;
                font-weight: bold;
            """)
        else:
            status_label = QLabel("待上课")
            status_label.setStyleSheet("""
                background-color: #ff9800;
                color: white;
                padding: 4px 8px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: bold;
            """)

        status_label.setAlignment(Qt.AlignCenter)
        status_label.setFixedSize(60, 20)
        status_layout.addWidget(status_label)
        status_layout.addStretch()

        # 中间课程信息
        info_layout = QVBoxLayout()
        info_layout.setSpacing(5)
        info_layout.setContentsMargins(0,0,0,0)

        course_name = QLabel(course_data.get('course_name', '未知课程'))
        course_name.setFont(QFont("微软雅黑", 10, QFont.Bold))

        # 组合显示班级和教室信息
        class_info = course_data.get('class_name', '未知班级')
        classroom_info = course_data.get('classroom_name', '')
        if classroom_info:
            class_info += f" | {classroom_info}"

        class_name = QLabel(class_info)
        class_name.setFont(QFont("微软雅黑", 10))
        class_name.setStyleSheet("color: #666;")

        # 添加时间信息
        time_info = f"{course_data.get('start_time', '')} - {course_data.get('end_time', '')}"
        if time_info.strip() != ' - ':
            time_label = QLabel(time_info)
            time_label.setFont(QFont("微软雅黑", 9))
            time_label.setStyleSheet("color: #999;")
            info_layout.addWidget(time_label)

        info_layout.addWidget(course_name)
        info_layout.addWidget(class_name)

        # 右侧进入课堂按钮
        if status == 'in_progress':
            enter_btn = QPushButton("进入课堂")
            enter_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4caf50;
                    color: white;
                    border: none;
                    border-radius: 20px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
        else:
            enter_btn = QPushButton("开始上课")
            enter_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e3f2fd;
                    color: #4a90e2;
                    border: none;
                    border-radius: 20px;
                }
                QPushButton:hover {
                    background-color: #d0e8fb;
                }
            """)

        enter_btn.setFont(QFont("微软雅黑", 11))
        enter_btn.setCursor(Qt.PointingHandCursor)
        enter_btn.setFixedSize(120, 40)

        # 绑定点击事件
        course_id = course_data.get('id')
        if course_id:
            enter_btn.clicked.connect(lambda: self.enter_classroom(course_id))

        card_layout.addWidget(status_widget)
        card_layout.addLayout(info_layout)
        card_layout.addStretch()
        card_layout.addWidget(enter_btn, alignment=Qt.AlignCenter)

        # 卡片阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setXOffset(0)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 30))
        card_widget.setGraphicsEffect(shadow)

        return card_widget

    def enter_classroom(self, course_id):
        """进入课堂"""
        try:
            # 关闭当前dashboard窗口
            self.close()

            # 启动Mastaji主程序，传递课程ID
            mastaji_window = MainWindow(teacher_info=self.teacher_info, current_course_id=course_id)
            mastaji_window.show()

        except Exception as e:
            QMessageBox.warning(self, '错误', f'进入课堂失败: {str(e)}')

    def logout(self):
        """退出登录"""
        reply = QMessageBox.question(
            self,
            '确认退出',
            '确定要退出登录吗？\n退出后需要重新登录才能使用系统。',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 清除登录管理器中的会话
                self.login_manager.logout()

                # 显示退出成功消息
                QMessageBox.information(self, '退出成功', '已成功退出登录！')

                # 关闭当前窗口
                self.close()

                # 重新启动登录流程
                self.restart_login()

            except Exception as e:
                QMessageBox.warning(self, '错误', f'退出登录时发生错误: {str(e)}')

    def restart_login(self):
        """重新启动登录流程"""
        try:
            from core.login_window import LoginWindow

            login_window = LoginWindow()

            def on_login_success(teacher_info):
                # 登录成功后重新打开dashboard
                dashboard = Dashboard(teacher_info=teacher_info)
                dashboard.show()

            login_window.login_success.connect(on_login_success)
            login_window.exec_()

        except Exception as e:
            print(f"重新启动登录流程失败: {e}")
            # 如果无法重新启动登录，则退出应用程序
            QApplication.instance().quit()


if __name__ == '__main__':
    app = QApplication(sys.argv)

    # 获取登录管理器
    login_manager = get_login_manager()

    # 如果已登录，直接显示dashboard
    if login_manager.is_logged_in:
        teacher_info = login_manager.get_teacher_info()
        window = Dashboard(teacher_info=teacher_info)
    else:
        # 未登录，使用空的teacher_info（会触发登录流程）
        window = Dashboard()

    window.show()
    sys.exit(app.exec_())