#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试投屏模块修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from modules.screen_dlan_mod import ScreenDLAN

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.screen_dlan = None
        self.init_ui()
        
        # 确保应用程序不会因为子窗口关闭而退出
        app = QApplication.instance()
        if app:
            app.setQuitOnLastWindowClosed(False)
    
    def init_ui(self):
        self.setWindowTitle('投屏模块测试')
        self.setGeometry(100, 100, 300, 200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # 状态标签
        self.status_label = QLabel('投屏模块测试程序')
        layout.addWidget(self.status_label)
        
        # 打开投屏按钮
        self.open_btn = QPushButton('打开投屏模块')
        self.open_btn.clicked.connect(self.open_screen_dlan)
        layout.addWidget(self.open_btn)
        
        # 关闭投屏按钮
        self.close_btn = QPushButton('关闭投屏模块')
        self.close_btn.clicked.connect(self.close_screen_dlan)
        layout.addWidget(self.close_btn)
        
        # 退出按钮
        self.quit_btn = QPushButton('退出测试')
        self.quit_btn.clicked.connect(self.quit_app)
        layout.addWidget(self.quit_btn)
        
        central_widget.setLayout(layout)
    
    def open_screen_dlan(self):
        """打开投屏模块"""
        try:
            if self.screen_dlan is None:
                print("创建新的投屏模块实例...")
                self.screen_dlan = ScreenDLAN()
            
            print("显示投屏模块...")
            self.screen_dlan.show()
            self.status_label.setText('投屏模块已打开')
            
        except Exception as e:
            print(f"打开投屏模块失败: {e}")
            self.status_label.setText(f'打开失败: {e}')
    
    def close_screen_dlan(self):
        """关闭投屏模块"""
        try:
            if self.screen_dlan:
                print("安全关闭投屏模块...")
                self.screen_dlan.close_safely()
                self.status_label.setText('投屏模块已关闭')
            else:
                self.status_label.setText('投屏模块未打开')
                
        except Exception as e:
            print(f"关闭投屏模块失败: {e}")
            self.status_label.setText(f'关闭失败: {e}')
    
    def quit_app(self):
        """退出应用程序"""
        print("退出测试程序...")
        if self.screen_dlan:
            try:
                self.screen_dlan.close_safely()
            except:
                pass
        QApplication.instance().quit()

def main():
    """主函数"""
    # 高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(True)  # 测试程序可以正常退出
    
    window = TestMainWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
